report 50102 "OpportunitiesCreatedReport"
{
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = All;
    Caption = 'Weekly Created Opportunities Report';
    DefaultRenderingLayout = OpportunitiesExcelLayout;

    dataset
    {
        dataitem(Opportunity; Opportunity)
        {
            column(No; "No.") { }
            column(Description; Description) { }
            column(Type_of_Opportunity; Type_of_Project__c) { }
            column(Salesperson_Code; "Salesperson Code") { }
            column(Technician_Group; Technician_Group__c) { }
            column(Client_Name; "Contact Name") { }
            column(Partner; Partner_For_The_Project__c) { }
            column(Sales_Cycle_Code; "Sales Cycle Code") { }
            column(Current_Sales_Cycle_Stage; "Current Sales Cycle Stage") { } // Debugging column
            column(Current_Sales_Cycle_Stage_Description; StageDescription) { } // Use variable for description
            column(Creation_Date; "Creation Date") { }
            column(Estimated_Closing_Date; "Estimated Closing Date") { }
            column(Date_Closed; "Date Closed") { }
            column(Amount; Amount) { }
            column(<PERSON><PERSON>; "Margin_Amount__c") { }
            column(Gross_Profit; GrossProfit) { }

            trigger OnPreDataItem()
            var
                StartDate: Date;
                Today: Date;
                LastSat: Date;
            begin
                // Calculate last Saturday (2 days before the start of current week)
                LastSat := CalcDate('<-2D>', CalcDate('<-CW>', WorkDate()));

                // Set start date to last Saturday
                StartDate := LastSat;

                // Set end date to today
                Today := WorkDate();

                // Filter for Creation Date between start date and today
                SetRange("Creation Date", StartDate, Today);
            end;

            trigger OnAfterGetRecord()
            var
                SalesCycleStage: Record "Sales Cycle Stage";
            begin
                // Clear variable
                StageDescription := '';

                // Manually fetch the stage description
                if SalesCycleStage.Get("Sales Cycle Code", "Current Sales Cycle Stage") then
                    StageDescription := SalesCycleStage.Description
                else
                    StageDescription := '';
            end;
        }
    }

    rendering
    {
        layout(OpportunitiesExcelLayout)
        {
            Type = Excel;
            LayoutFile = 'OpportunitiesExcelLayout2.xlsx';
            Caption = 'Excel Layout for Opportunities';
            Summary = 'Excel layout for the Opportunities custom report';
        }
    }
    var
        StageDescription: Text[100]; // Variable to hold the stage description
}