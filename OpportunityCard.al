namespace DefaultPublisher.ALProject1;

using Microsoft.Sales.Customer;
using Microsoft.CRM.Opportunity;
using System.Text;
using System.Reflection;
using Microsoft.CRM.BusinessRelation;

tableextension 50100 "MyOpportunityExt" extends "Opportunity"
{
    fields
    {
        field(50100; "Amount"; Decimal)
        {
            Caption = 'Amount (Selling Price)';
            trigger OnValidate()
            begin
                CalculateMargins();
                CalculateGrossProfit();
            end;
        }
        field(50101; "Cost__c"; Decimal)
        {
            Caption = 'Cost';
            trigger OnValidate()
            begin
                CalculateMargins();
                CalculateGrossProfit();
            end;
        }
        field(50102; "Margin_Amount__c"; Decimal)
        {
            Caption = 'Margin ($)';
        }
        field(50103; "Margin_Percent__c"; Decimal)
        {
            Caption = 'Margin (%)';
        }
        field(50104; "Type_of_Project__c"; Option)
        {
            Caption = 'Type of Opportunity';
            OptionMembers = "None","Hardware with support","Hardware without support","Hardware + services","Service","Managed Service","Software","Tender Offer";
            trigger OnValidate()
            begin
                CalculateGrossProfit();
                ValidateRules();
            end;
        }
        field(50132; "Type_of_Technology__c"; Text[250])
        {
            Caption = 'Type of Technology';
            Editable = false;
            trigger OnValidate()
            begin
                ValidateTechnologyTypes();
            end;
        }
        field(50105; "Type_Of_Support__c"; Option)
        {
            Caption = 'Type of Support';
            OptionMembers = "None","Installation","Renewal","New license","New Managed Service","Other";
            trigger OnValidate()
            begin
                ValidateRules();
            end;
        }
        field(50106; "Type_Of_Service__c"; Option)
        {
            Caption = 'Type of Service';
            OptionMembers = "None","Prepaid","Project";
        }
        field(50107; "Type_Of_License__c"; Option)
        {
            Caption = 'Type of License';
            OptionMembers = "None","MSP","Non-MSP";
        }
        field(50108; "PnL_Total_Hours__c"; Decimal)
        {
            Caption = 'Total PnL Hours';
        }
        field(50109; "Partner_For_The_Project__c"; Text[255])
        {
            Caption = 'Partner';
            TableRelation = "Contact Business Relation"."Contact Name" WHERE("Business Relation Code" = CONST('PARTNER'));
            ValidateTableRelation = true;
            trigger OnLookup()
            var
                ContactBusRel: Record "Contact Business Relation";
            begin
                ContactBusRel.SetRange("Business Relation Code", 'PARTNER');
                if Page.RunModal(Page::"Contact Business Relations", ContactBusRel) = Action::LookupOK then
                    Rec."Partner_For_The_Project__c" := ContactBusRel."Contact Name";
                CalculateGrossProfit();
            end;
        }
        field(50110; "Manufacturer__c"; Option)
        {
            Caption = 'Distributor';
            OptionMembers = "Autres","Arrow","Dell","Exclusive Network","Ingrammicro","Prival","Securitas","TDSynnex";
        }
        field(50111; "Serial_Number__c"; Text[2048])
        {
            Caption = 'Serial Number';
            OptimizeForTextSearch = true;
        }
        field(50112; "Support_Start_Date__c"; Date)
        {
            Caption = 'Support Start Date';
            trigger OnValidate()
            begin
                ValidateRules();
            end;
        }
        field(50113; "Support_End_Date__c"; Date)
        {
            Caption = 'Support End Date';
            trigger OnValidate()
            begin
                ValidateRules();
            end;
        }
        field(50114; "Renewal_Date__c"; Date)
        {
            Caption = 'Renewal Date';
        }
        field(50115; "Fiscal_Week__c"; Integer)
        {
            Caption = 'Fiscal Week';
        }
        field(50116; "Fiscal_Year_Start__c"; Date)
        {
            Caption = 'Fiscal Year Start';
        }
        field(50117; "Fiscal_Year_End__c"; Date)
        {
            Caption = 'Fiscal Year End';
        }
        field(50118; "Tender_Offer_ID__c"; Text[255])
        {
            Caption = 'Tender Offer ID';
        }
        field(50119; "Technician_Group__c"; Option)
        {
            Caption = 'Technician Group';
            OptionMembers = "None","Tech Network","Tech System","Tech Monitoring","Tech Cybersecurity","Tech RTLS";
        }
        field(50120; "Technician_Assigned__c"; Text[255])
        {
            Caption = 'Technician Assigned';
        }
        field(50121; "Internal_Project_Name__c"; Text[255])
        {
            Caption = 'Internal Project Name';
        }
        field(50122; "Follow_Up_Reminder__c"; Date)
        {
            Caption = 'Follow Up Reminder';
        }
        field(50123; "ERP_Quote_Number__c"; Text[255])
        {
            Caption = 'Quote Number';
            OptimizeForTextSearch = true;
        }
        field(50124; "Partner_Quote_Number__c"; Text[255])
        {
            Caption = 'Partner Quote Number';
            OptimizeForTextSearch = true;
        }
        field(50125; "Client_PO_Number__c"; Text[255])
        {
            Caption = 'Client PO Number';
            OptimizeForTextSearch = true;
        }
        field(50126; "Internal_PO_Number__c"; Text[255])
        {
            Caption = 'Internal PO Number';
            OptimizeForTextSearch = true;
        }
        field(50127; "Invoice__c"; Text[255])
        {
            Caption = 'Invoice';
            OptimizeForTextSearch = true;
        }
        field(50128; "SalesforceOppId"; Text[255])
        {
            Caption = 'Salesforce Opportunity ID';
        }
        field(50129; "SalesforceOppDesc"; BLOB)
        {
            Caption = 'Salesforce Description';
        }
        field(50130; "GrossProfit"; Decimal)
        {
            Caption = 'Gross Profit';
        }
        field(50131; "CloseDate"; Date)
        {
            Caption = 'Close Date';
        }
    }



    local procedure ValidateRules()
    begin
        // Rule 1: If "Hardware with support" or "Software", "Type of Support" cannot be "None"
        if (("Type_of_Project__c" = "Type_of_Project__c"::"Hardware with support") or
            ("Type_of_Project__c" = "Type_of_Project__c"::"Software") or
            ("Type_of_Project__c" = "Type_of_Project__c"::"Managed Service")) and
           ("Type_Of_Support__c" = "Type_Of_Support__c"::"None") then
            FieldError("Type_Of_Support__c", ' cannot be "None" when Type of Opportunity is "Hardware with support", "Managed Service" or "Software".');

        // Rule 2: If "Renewal", mandatory fields
        if ("Type_Of_Support__c" = "Type_Of_Support__c"::"Renewal") then begin
            if "Support_Start_Date__c" = 0D then begin
                FieldError("Support_Start_Date__c", ' is mandatory when Type of Support is "Renewal".');
            end;
            if "Support_End_Date__c" = 0D then begin
                FieldError("Support_End_Date__c", ' is mandatory when Type of Support is "Renewal".');
            end;
        end;
    end;

    local procedure ValidateTechnologyTypes()
    var
        TechnologyType: Text;
        TechnologyTypes: List of [Text];
        ValidTypes: List of [Text];
    begin
        if "Type_of_Technology__c" = '' then
            exit;

        ValidTypes.Add('Cyber Security');
        ValidTypes.Add('Network');
        ValidTypes.Add('Monitoring');
        ValidTypes.Add('RTLS');
        ValidTypes.Add('System Infra');
        ValidTypes.Add('Other');

        TechnologyTypes := "Type_of_Technology__c".Split(',');
        foreach TechnologyType in TechnologyTypes do begin
            TechnologyType := DelChr(TechnologyType, '<>', ' ');
            if not ValidTypes.Contains(TechnologyType) then
                Error('Invalid technology type: %1. Valid types are: Cyber Security, Network, Monitoring, RTLS, System Infra, Other', TechnologyType);
        end;
    end;

    local procedure CalculateMargins()
    begin
        "Margin_Amount__c" := "Amount" - "Cost__c";
        if "Amount" <> 0 then
            "Margin_Percent__c" := ("Margin_Amount__c" / "Amount") * 100
        else
            "Margin_Percent__c" := 0;
    end;

    procedure CalculateGrossProfit() CalcDescription: Text
    begin
        case "Type_of_Project__c" of
            "Type_of_Project__c"::Service:
                begin
                    if "Partner_For_The_Project__c" = 'Blesk' then
                        "GrossProfit" := "Amount" * 0.20
                    else
                        "GrossProfit" := "Amount" * 0.45;
                end;
            "Type_of_Project__c"::"Managed Service":
                begin
                    "GrossProfit" := "Amount" * 0.20;
                end;
            else
                if "Partner_For_The_Project__c" = 'Blesk' then
                    "GrossProfit" := "Amount" * 0.20
                else
                    "GrossProfit" := "Margin_Amount__c";
        end;
    end;
}

pageextension 50100 "MyOpportunityCardExt" extends "Opportunity Card"
{
    layout
    {
        addafter("General")
        {
            group("SFHistoricalData")
            {
                Caption = 'SF - Historical Data';
                field("Amount"; Rec."Amount")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the selling price to customer.';
                }
                field("Cost__c"; Rec."Cost__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the cost.';
                }
                field("Margin_Amount__c"; Rec."Margin_Amount__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the margin amount.';
                    Editable = false;
                }
                field("Margin_Percent__c"; Rec."Margin_Percent__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the margin percentage.';
                    Editable = false;
                }
                field("GrossProfit"; Rec."GrossProfit")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the gross profit. If service is selected, the gross profit is 45% of the amount. If managed service is selected, the gross profit is 20% of the amount. If Blesk is selected as a partner, the gross profit is 20% of the amount. Otherwise, it is equal to the margin amount.';
                    Editable = false;
                }
                field("Type_of_Project__c"; Rec."Type_of_Project__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type of opportunity.';
                }
                field("Type_of_Technology__c"; Rec."Type_of_Technology__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type(s) of technology. Click the lookup button to select multiple options.';
                    trigger OnAssistEdit()
                    begin
                        SelectTechnologyTypes();
                    end;
                }
                field("Type_Of_Support__c"; Rec."Type_Of_Support__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type of support.';
                }
                field("Type_Of_Service__c"; Rec."Type_Of_Service__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type of service.';
                }
                field("Type_Of_License__c"; Rec."Type_Of_License__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type of license.';
                }
                field("PnL_Total_Hours__c"; Rec."PnL_Total_Hours__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the total PnL hours.';
                }
                field("Partner_For_The_Project__c"; Rec."Partner_For_The_Project__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the partner for the project.';
                }
                field("Manufacturer__c"; Rec."Manufacturer__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the distributor.';
                }
                field("Serial_Number__c"; Rec."Serial_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the serial number.';
                    MultiLine = true;
                }
                field("Support_Start_Date__c"; Rec."Support_Start_Date__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the support start date.';
                }
                field("Support_End_Date__c"; Rec."Support_End_Date__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the support end date.';
                }
                field("CloseDate"; Rec."CloseDate")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the close date.';
                }
                field("Renewal_Date__c"; Rec."Renewal_Date__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the renewal date.';
                }
                field("Fiscal_Week__c"; Rec."Fiscal_Week__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the fiscal week.';
                }
                field("Fiscal_Year_Start__c"; Rec."Fiscal_Year_Start__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the fiscal year start date.';
                }
                field("Fiscal_Year_End__c"; Rec."Fiscal_Year_End__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the fiscal year end date.';
                }
                field("Tender_Offer_ID__c"; Rec."Tender_Offer_ID__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the tender offer ID.';
                }
                field("Technician_Group__c"; Rec."Technician_Group__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the technician group.';
                }
                field("Technician_Assigned__c"; Rec."Technician_Assigned__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the assigned technician.';
                }
                field("Internal_Project_Name__c"; Rec."Internal_Project_Name__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the internal project name.';
                }
                field("Follow_Up_Reminder__c"; Rec."Follow_Up_Reminder__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the follow up reminder date.';
                }
                field("ERP_Quote_Number__c"; Rec."ERP_Quote_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the quote number.';
                }
                field("Partner_Quote_Number__c"; Rec."Partner_Quote_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the partner quote number.';
                }
                field("Client_PO_Number__c"; Rec."Client_PO_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the client PO number.';
                }
                field("Internal_PO_Number__c"; Rec."Internal_PO_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the internal PO number.';
                }
                field("Invoice__c"; Rec."Invoice__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the invoice.';
                }
                field("Salesforce OpportunityId"; Rec."SalesforceOppId")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the ID of the corresponding opportunity in Salesforce.';
                }
                field("SalesforceOppDesc"; LargeText)
                {
                    Caption = 'SF Description';
                    ApplicationArea = All;
                    MultiLine = true;
                    trigger OnValidate()
                    begin
                        SetLargeText(LargeText);
                    end;
                }

            }
        }
    }



    var
        LargeText: Text;
        TempSupportTypeBeforeChange: Option "None","Installation","License Renewal","New license","Other";

    trigger OnAfterGetRecord()
    begin
        LargeText := GetLargeText();
        Rec.CalculateGrossProfit();
    end;

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    begin
        // Validate mandatory fields when user tries to save (OK/LookupOK)
        // But allow exit without saving (Cancel/other actions)
        if CloseAction in [ACTION::OK, ACTION::LookupOK] then begin
            // Only validate for new opportunities (Creation Date = 0D means new record)
            if Rec."Creation Date" = 0D then begin
                if Rec."Contact No." = '' then begin
                    Message('Contact No. is mandatory for creating an opportunity.');
                    exit(false);
                end;

                if Rec."Type_of_Project__c" = Rec."Type_of_Project__c"::"None" then begin
                    Message('Type of Opportunity is mandatory for creating an opportunity.');
                    exit(false);
                end;

                if Rec."Type_of_Technology__c" = '' then begin
                    Message('Type of Technology is mandatory for creating an opportunity.');
                    exit(false);
                end;
            end;
        end;
        // Always allow exit for Cancel or other actions, and for existing opportunities
        exit(true);
    end;



    procedure SetLargeText(NewLargeText: Text)
    var
        OutStream: OutStream;
    begin
        Clear(Rec."SalesforceOppDesc");
        Rec."SalesforceOppDesc".CreateOutStream(OutStream, TEXTENCODING::UTF8);
        OutStream.WriteText(LargeText);
        Rec.Modify();
    end;

    procedure GetLargeText() NewLargeText: Text
    var
        TypeHelper: Codeunit "Type Helper";
        InStream: InStream;
    begin
        Rec.CalcFields("SalesforceOppDesc");
        Rec."SalesforceOppDesc".CreateInStream(InStream, TEXTENCODING::UTF8);
        exit(TypeHelper.TryReadAsTextWithSepAndFieldErrMsg(InStream, TypeHelper.LFSeparator(), Rec.FieldName("SalesforceOppDesc")));
    end;

    local procedure SelectTechnologyTypes()
    var
        TechnologyType: Text;
        TechnologyTypes: List of [Text];
        TempTechnologyType: Record "Technology Type" temporary;
        TechnologyTypePage: Page "Technology Type Selection";
    begin
        if Rec."Type_of_Technology__c" <> '' then
            TechnologyTypes := Rec."Type_of_Technology__c".Split(',');

        // Initialize temporary record with options
        TempTechnologyType.Init();
        TempTechnologyType.Code := 'CYBER';
        TempTechnologyType.Description := 'Cyber Security';
        TempTechnologyType.Insert();

        TempTechnologyType.Init();
        TempTechnologyType.Code := 'NETWORK';
        TempTechnologyType.Description := 'Network';
        TempTechnologyType.Insert();

        TempTechnologyType.Init();
        TempTechnologyType.Code := 'MONITOR';
        TempTechnologyType.Description := 'Monitoring';
        TempTechnologyType.Insert();

        TempTechnologyType.Init();
        TempTechnologyType.Code := 'RTLS';
        TempTechnologyType.Description := 'RTLS';
        TempTechnologyType.Insert();

        TempTechnologyType.Init();
        TempTechnologyType.Code := 'SYSINFRA';
        TempTechnologyType.Description := 'System Infra';
        TempTechnologyType.Insert();

        TempTechnologyType.Init();
        TempTechnologyType.Code := 'OTHER';
        TempTechnologyType.Description := 'Other';
        TempTechnologyType.Insert();

        // Set selected values
        foreach TechnologyType in TechnologyTypes do begin
            TechnologyType := DelChr(TechnologyType, '<>', ' ');
            TempTechnologyType.Reset();
            TempTechnologyType.SetRange(Description, TechnologyType);
            if TempTechnologyType.FindFirst() then begin
                TempTechnologyType.Selected := true;
                TempTechnologyType.Modify();
            end;
        end;

        TempTechnologyType.Reset();
        TechnologyTypePage.SetTempRecord(TempTechnologyType);
        if TechnologyTypePage.RunModal() = Action::OK then begin
            Clear(TempTechnologyType);
            TechnologyTypePage.GetSelectedTypes(TempTechnologyType);

            Rec."Type_of_Technology__c" := '';
            if TempTechnologyType.FindSet() then
                repeat
                    if Rec."Type_of_Technology__c" = '' then
                        Rec."Type_of_Technology__c" := TempTechnologyType.Description
                    else
                        Rec."Type_of_Technology__c" += ',' + TempTechnologyType.Description;
                until TempTechnologyType.Next() = 0;

            Rec.Modify();
            CurrPage.Update();
        end;
    end;
}