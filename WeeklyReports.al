report 50101 "OpportunitiesWonReport"
{
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = All;
    Caption = 'Weekly Won Opportunities Report';
    DefaultRenderingLayout = OpportunitiesExcelLayout;

    dataset
    {
        dataitem(Opportunity; Opportunity)
        {
            column(No; "No.") { }
            column(Description; Description) { }
            column(Type_of_Opportunity; Type_of_Project__c) { }
            column(Salesperson_Code; "Salesperson Code") { }
            column(Technician_Group; Technician_Group__c) { }
            column(Client_Name; "Contact Name") { }
            column(Partner; Partner_For_The_Project__c) { }
            column(Sales_Cycle_Code; "Sales Cycle Code") { }
            column(Creation_Date; "Creation Date") { }
            column(Date_Closed; "Date Closed") { }
            column(Amount; Amount) { }
            column(Margin; "Margin_Amount__c") { }
            column(Gross_Profit; GrossProfit) { }

            trigger OnPreDataItem()
            var
                StartDate: Date;
                Today: Date;
                LastSat: Date;
            begin
                // Filter for closed Won opportunities
                SetRange("Status", Status::Won);

                // Calculate last Saturday (2 days before the start of current week)
                LastSat := CalcDate('<-2D>', CalcDate('<-CW>', WorkDate()));

                // Set start date to last Friday
                StartDate := LastSat;

                // Set end date to today
                Today := WorkDate();

                // Filter for Date Closed between start date and today
                SetRange("Date Closed", StartDate, Today);
            end;
        }
    }

    rendering
    {
        layout(OpportunitiesExcelLayout)
        {
            Type = Excel;
            LayoutFile = 'OpportunitiesExcelLayout.xlsx';
            Caption = 'Excel Layout for Opportunities';
            Summary = 'Excel layout for the Opportunities custom report';
        }
    }
}