report 50104 "ManualOppReport"
{
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = All;
    Caption = 'Manual Opportunities Report';
    DefaultRenderingLayout = OpportunitiesExcelLayout;

    dataset
    {
        dataitem(Opportunity; Opportunity)
        {
            DataItemTableView = sorting("No.");
            RequestFilterFields = "No.", "Creation Date", "Salesperson Code";

            column(No; "No.") { }
            column(Description; Description) { }
            column(Type_of_Opportunity; Type_of_Project__c) { }
            column(Salesperson_Code; "Salesperson Code") { }
            column(Technician_Group; Technician_Group__c) { }
            column(Client_Name; "Contact Name") { }
            column(Contact_Email; ContactEmails) { }
            column(Partner; Partner) { }
            column(Sales_Cycle_Code; "Sales Cycle Code") { }
            column(Current_Sales_Cycle_Stage; "Current Sales Cycle Stage") { }
            column(Current_Sales_Cycle_Stage_Description; StageDescription) { }
            column(Creation_Date; "Creation Date") { }
            column(Estimated_Closing_Date; "Estimated Closing Date") { }
            column(Date_Closed; "Date Closed") { }
            column(Amount; Amount) { }
            column(Margin; Margin_Amount__c) { }
            column(Gross_Profit; GrossProfit) { }
            column(Debug_Info; DebugInfo) { }

            trigger OnPreDataItem()
            begin
                // Manually add partner filter for explicit filtering
                if PartnersToInclude <> '' then begin
                    SetFilter(Partner_For_The_Project__c, PartnersToInclude);
                    DebugInfo := 'Partner filter applied: ' + PartnersToInclude;
                end else
                    DebugInfo := 'No partner filter applied';
            end;

            trigger OnAfterGetRecord()
            var
                SalesCycleStage: Record "Sales Cycle Stage";
                Contact: Record Contact;
                TempContact: Record Contact temporary;
                CompanyContact: Record Contact;
                EmailCount: Integer;
                MaxEmails: Integer;
            begin
                // Clear variables
                StageDescription := '';
                ContactEmails := '';
                Partner := Partner_For_The_Project__c;
                Clear(TempContact);
                EmailCount := 0;
                MaxEmails := 3; // Maximum number of emails to include

                // Manually fetch the stage description
                if SalesCycleStage.Get("Sales Cycle Code", "Current Sales Cycle Stage") then
                    StageDescription := SalesCycleStage.Description
                else
                    StageDescription := '';

                // First, ensure we get the primary contact's email
                if "Contact No." <> '' then begin
                    if Contact.Get("Contact No.") then begin
                        if Contact."E-Mail" <> '' then begin
                            ContactEmails := Contact."E-Mail";
                            EmailCount += 1;
                        end;

                        // Get other contacts for this company and store in temp table for sorting
                        if Contact."Company No." <> '' then begin
                            CompanyContact.Reset();
                            CompanyContact.SetRange("Company No.", Contact."Company No.");
                            // Skip the primary contact
                            CompanyContact.SetFilter("No.", '<>%1', Contact."No.");
                            if CompanyContact.FindSet() then
                                repeat
                                    if CompanyContact."E-Mail" <> '' then begin
                                        // Store in temp table for sorting
                                        TempContact.Init();
                                        TempContact."No." := CompanyContact."No.";
                                        TempContact."E-Mail" := CompanyContact."E-Mail";
                                        TempContact."Last Date Modified" := CompanyContact."Last Date Modified";
                                        TempContact.Insert();
                                    end;
                                until CompanyContact.Next() = 0;

                            // Sort by last modified date to get most recently updated contacts first
                            TempContact.Reset();
                            TempContact.SetCurrentKey("Last Date Modified");
                            TempContact.Ascending(false); // Most recent first

                            // Add most recent emails until we reach our limit
                            if TempContact.FindSet() then
                                repeat
                                    if EmailCount < MaxEmails then begin
                                        if ContactEmails = '' then
                                            ContactEmails := TempContact."E-Mail"
                                        else
                                            ContactEmails := CopyStr(ContactEmails + '; ' + TempContact."E-Mail", 1, 250);

                                        EmailCount += 1;
                                    end;
                                until (TempContact.Next() = 0) or (EmailCount = MaxEmails);
                        end;
                    end;
                end;

                // Add debug info for troubleshooting
                DebugInfo := DebugInfo + ', Partner: ' + Partner;
            end;
        }
    }

    rendering
    {
        layout(OpportunitiesExcelLayout)
        {
            Type = Excel;
            LayoutFile = 'OpportunitiesExcelLayout4.xlsx';
            Caption = 'Excel Layout for Opportunities';
            Summary = 'Excel layout for the Open Opportunities custom report';
        }
    }

    var
        StageDescription: Text[100]; // Variable to hold the stage description
        ContactEmails: Text[250]; // Variable to hold multiple contact emails
        DebugInfo: Text; // Variable for debugging
        PartnersToInclude: Text; // For partner filtering
        Partner: Text[250]; // For storing partner value for display

    procedure SetPartnerFilter(PartnerFilter: Text)
    begin
        PartnersToInclude := PartnerFilter;
    end;
}